/**
 * ComponentBadge Atom
 * Badge component for displaying component status with accessibility support
 */

import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import { cva, type VariantProps } from 'class-variance-authority'
import React from 'react'

// Badge variants using CVA for consistent styling
const componentBadgeVariants = cva(
  'inline-flex items-center gap-1 text-xs font-medium transition-colors',
  {
    variants: {
      status: {
        active: 'bg-green-100 text-green-800 border-green-200 hover:bg-green-200',
        inactive: 'bg-gray-100 text-gray-600 border-gray-200 hover:bg-gray-200',
        preferred: 'bg-blue-100 text-blue-800 border-blue-200 hover:bg-blue-200',
        available: 'bg-green-100 text-green-700 border-green-200',
        limited: 'bg-yellow-100 text-yellow-700 border-yellow-200',
        out_of_stock: 'bg-red-100 text-red-700 border-red-200',
        discontinued: 'bg-gray-100 text-gray-500 border-gray-200',
        on_order: 'bg-blue-100 text-blue-700 border-blue-200',
      },
      size: {
        sm: 'px-1.5 py-0.5 text-xs',
        md: 'px-2 py-1 text-sm',
        lg: 'px-3 py-1.5 text-base',
      },
      variant: {
        default: 'border',
        solid: 'border-transparent',
        outline: 'bg-transparent border-2',
      },
    },
    defaultVariants: {
      status: 'active',
      size: 'md',
      variant: 'default',
    },
  }
)

// Status display configuration
const statusConfig = {
  active: { label: 'Active', icon: '●' },
  inactive: { label: 'Inactive', icon: '○' },
  preferred: { label: 'Preferred', icon: '★' },
  available: { label: 'Available', icon: '●' },
  limited: { label: 'Limited Stock', icon: '◐' },
  out_of_stock: { label: 'Out of Stock', icon: '○' },
  discontinued: { label: 'Discontinued', icon: '✕' },
  on_order: { label: 'On Order', icon: '◔' },
} as const

export interface ComponentBadgeProps
  extends React.HTMLAttributes<HTMLSpanElement>,
    VariantProps<typeof componentBadgeVariants> {
  status: keyof typeof statusConfig
  showIcon?: boolean
  showLabel?: boolean
  customLabel?: string
  pulse?: boolean
  'data-testid'?: string
}

export const ComponentBadge = React.forwardRef<HTMLSpanElement, ComponentBadgeProps>(
  (
    {
      status,
      size,
      variant,
      showIcon = true,
      showLabel = true,
      customLabel,
      pulse = false,
      className,
      'data-testid': testId,
      ...props
    },
    ref
  ) => {
    const safeStatus = status || 'inactive' // Default to inactive if status is undefined
    const config = statusConfig[safeStatus] || statusConfig.inactive // Fallback to inactive for invalid status
    const label = customLabel || config.label

    return (
      <Badge
        ref={ref}
        className={cn(
          componentBadgeVariants({ status: safeStatus, size, variant }),
          pulse && 'animate-pulse',
          className
        )}
        role="status"
        aria-label={`Status: ${label}`}
        data-testid={testId || `component-badge-${safeStatus}`}
        {...props}
      >
        {showIcon && (
          <span
            className="inline-block"
            aria-hidden="true"
            data-testid={`${testId || 'component-badge'}-icon`}
          >
            {config.icon}
          </span>
        )}
        {showLabel && <span data-testid={`${testId || 'component-badge'}-label`}>{label}</span>}
      </Badge>
    )
  }
)

ComponentBadge.displayName = 'ComponentBadge'

// Convenience components for common statuses
export const ActiveBadge = (props: Omit<ComponentBadgeProps, 'status'>) => (
  <ComponentBadge status="active" {...props} />
)

export const InactiveBadge = (props: Omit<ComponentBadgeProps, 'status'>) => (
  <ComponentBadge status="inactive" {...props} />
)

export const PreferredBadge = (props: Omit<ComponentBadgeProps, 'status'>) => (
  <ComponentBadge status="preferred" {...props} />
)

export const AvailableBadge = (props: Omit<ComponentBadgeProps, 'status'>) => (
  <ComponentBadge status="available" {...props} />
)

export const LimitedBadge = (props: Omit<ComponentBadgeProps, 'status'>) => (
  <ComponentBadge status="limited" {...props} />
)

export const OutOfStockBadge = (props: Omit<ComponentBadgeProps, 'status'>) => (
  <ComponentBadge status="out_of_stock" {...props} />
)

export const DiscontinuedBadge = (props: Omit<ComponentBadgeProps, 'status'>) => (
  <ComponentBadge status="discontinued" {...props} />
)

export const OnOrderBadge = (props: Omit<ComponentBadgeProps, 'status'>) => (
  <ComponentBadge status="on_order" {...props} />
)

// Hook for getting badge props from component data
export const useComponentBadgeProps = (component: {
  is_active?: boolean
  is_preferred?: boolean
  stock_status?: string
}): ComponentBadgeProps => {
  if (!component.is_active) {
    return { status: 'inactive' }
  }

  if (component.is_preferred) {
    return { status: 'preferred' }
  }

  // Map stock status to badge status
  const stockStatusMap: Record<string, keyof typeof statusConfig> = {
    available: 'available',
    limited: 'limited',
    out_of_stock: 'out_of_stock',
    discontinued: 'discontinued',
    on_order: 'on_order',
  }

  const badgeStatus = component.stock_status
    ? stockStatusMap[component.stock_status] || 'active'
    : 'active'

  return { status: badgeStatus }
}

// Export types for external use
export type ComponentBadgeStatus = keyof typeof statusConfig
export type ComponentBadgeSize = NonNullable<ComponentBadgeProps['size']>
export type ComponentBadgeVariant = NonNullable<ComponentBadgeProps['variant']>
