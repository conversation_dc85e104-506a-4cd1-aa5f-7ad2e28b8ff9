/**
 * @jest-environment jsdom
 */

import { useAuth } from '@/hooks/useAuth'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { render, screen, waitFor } from '@testing-library/react'
import React from 'react'
import { AdminDashboardOverview } from '../../components/AdminDashboardOverview'

// Mock the auth hook
vi.mock('@/hooks/useAuth')
const mockUseAuth = useAuth as vi.MockedFunction<typeof useAuth>

// Mock the admin dashboard data hook
vi.mock('../../hooks/useAdminDashboardData', () => ({
  useAdminDashboardData: () => ({
    data: null,
    isLoading: false,
    error: null,
  }),
}))

// Mock the lazy-loaded components
vi.mock('../../components/AuditLogsWidget', () => ({
  AuditLogsWidget: () => <div data-testid="audit-logs-widget">Audit Logs Widget</div>,
}))

vi.mock('../../components/SecurityMonitoringWidget', () => ({
  SecurityMonitoringWidget: () => (
    <div data-testid="security-monitoring-widget">Security Monitoring Widget</div>
  ),
}))

vi.mock('../../components/SystemConfigurationWidget', () => ({
  SystemConfigurationWidget: () => (
    <div data-testid="system-configuration-widget">System Configuration Widget</div>
  ),
}))

vi.mock('../../components/QuickAdminActionsWidget', () => ({
  QuickAdminActionsWidget: () => (
    <div data-testid="quick-admin-actions-widget">Quick Admin Actions Widget</div>
  ),
}))

const createTestQueryClient = () =>
  new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  })

const renderWithProviders = (component: React.ReactElement) => {
  const queryClient = createTestQueryClient()
  return render(<QueryClientProvider client={queryClient}>{component}</QueryClientProvider>)
}

describe('AdminDashboardOverview', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('Access Control', () => {
    it('should show access denied message for non-admin users', () => {
      mockUseAuth.mockReturnValue({
        user: { id: '1', name: 'Test User', email: '<EMAIL>' },
        isAdmin: () => false,
        isAuthenticated: () => true,
        login: vi.fn(),
        logout: vi.fn(),
        isLoading: false,
      })

      renderWithProviders(<AdminDashboardOverview />)

      expect(screen.getByText('Access Denied')).toBeInTheDocument()
      expect(
        screen.getByText("You don't have permission to access the admin dashboard.")
      ).toBeInTheDocument()
    })

    it('should render dashboard for admin users', () => {
      mockUseAuth.mockReturnValue({
        user: { id: '1', name: 'Admin User', email: '<EMAIL>' },
        isAdmin: () => true,
        isAuthenticated: () => true,
        login: vi.fn(),
        logout: vi.fn(),
        isLoading: false,
      })

      renderWithProviders(<AdminDashboardOverview />)

      expect(screen.getByText('Admin Dashboard')).toBeInTheDocument()
      expect(
        screen.getByText(
          'Welcome back, Admin User. Monitor and manage your Ultimate Electrical Designer system.'
        )
      ).toBeInTheDocument()
    })
  })

  describe('Dashboard Layout', () => {
    beforeEach(() => {
      mockUseAuth.mockReturnValue({
        user: { id: '1', name: 'Admin User', email: '<EMAIL>' },
        isAdmin: () => true,
        isAuthenticated: () => true,
        login: vi.fn(),
        logout: vi.fn(),
        isLoading: false,
      })
    })

    it('should render core widgets', () => {
      renderWithProviders(<AdminDashboardOverview />)

      // Core widgets should be rendered immediately
      expect(screen.getByText('System Metrics')).toBeInTheDocument()
      expect(screen.getByText('User Management')).toBeInTheDocument()
      expect(screen.getByText('Component Library')).toBeInTheDocument()
      expect(screen.getByText('Project Oversight')).toBeInTheDocument()
    })

    it('should lazy load advanced widgets', async () => {
      renderWithProviders(<AdminDashboardOverview />)

      // Advanced widgets should be lazy loaded
      await waitFor(() => {
        expect(screen.getByTestId('audit-logs-widget')).toBeInTheDocument()
        expect(screen.getByTestId('security-monitoring-widget')).toBeInTheDocument()
        expect(screen.getByTestId('system-configuration-widget')).toBeInTheDocument()
        expect(screen.getByTestId('quick-admin-actions-widget')).toBeInTheDocument()
      })
    })

    it('should display last updated timestamp', () => {
      renderWithProviders(<AdminDashboardOverview />)

      expect(screen.getByText(/Last updated:/)).toBeInTheDocument()
    })
  })

  describe('Error Handling', () => {
    beforeEach(() => {
      mockUseAuth.mockReturnValue({
        user: { id: '1', name: 'Admin User', email: '<EMAIL>' },
        isAdmin: () => true,
        isAuthenticated: () => true,
        login: vi.fn(),
        logout: vi.fn(),
        isLoading: false,
      })
    })

    it('should handle dashboard error gracefully', () => {
      const error = new Error('Failed to load dashboard data')

      renderWithProviders(<AdminDashboardOverview error={error} />)

      expect(screen.getByText('Dashboard Error')).toBeInTheDocument()
      expect(screen.getByText('Failed to load dashboard data')).toBeInTheDocument()
    })

    it('should show generic error message for unknown errors', () => {
      const error = new Error()

      renderWithProviders(<AdminDashboardOverview error={error} />)

      expect(screen.getByText('Dashboard Error')).toBeInTheDocument()
      expect(screen.getByText('Failed to load admin dashboard data.')).toBeInTheDocument()
    })
  })

  describe('Loading States', () => {
    beforeEach(() => {
      mockUseAuth.mockReturnValue({
        user: { id: '1', name: 'Admin User', email: '<EMAIL>' },
        isAdmin: () => true,
        isAuthenticated: () => true,
        login: vi.fn(),
        logout: vi.fn(),
        isLoading: false,
      })
    })

    it('should show refreshing indicator when loading', () => {
      renderWithProviders(<AdminDashboardOverview isLoading={true} />)

      expect(screen.getByText('Refreshing...')).toBeInTheDocument()
    })
  })

  describe('Responsive Design', () => {
    beforeEach(() => {
      mockUseAuth.mockReturnValue({
        user: { id: '1', name: 'Admin User', email: '<EMAIL>' },
        isAdmin: () => true,
        isAuthenticated: () => true,
        login: vi.fn(),
        logout: vi.fn(),
        isLoading: false,
      })
    })

    it('should apply custom className', () => {
      const { container } = renderWithProviders(<AdminDashboardOverview className="custom-class" />)

      expect(container.firstChild).toHaveClass('custom-class')
    })

    it('should have responsive grid classes', () => {
      renderWithProviders(<AdminDashboardOverview />)

      // Check for responsive grid classes in the DOM
      const gridElements = document.querySelectorAll('.grid')
      expect(gridElements.length).toBeGreaterThan(0)
    })
  })

  describe('Widget Error Boundaries', () => {
    beforeEach(() => {
      mockUseAuth.mockReturnValue({
        user: { id: '1', name: 'Admin User', email: '<EMAIL>' },
        isAdmin: () => true,
        isAuthenticated: () => true,
        login: vi.fn(),
        logout: vi.fn(),
        isLoading: false,
      })
    })

    it('should isolate widget errors', () => {
      // This test would require more complex error boundary testing
      // For now, we verify that error boundaries are in place
      renderWithProviders(<AdminDashboardOverview />)

      // Verify that the dashboard renders even if individual widgets might fail
      expect(screen.getByText('Admin Dashboard')).toBeInTheDocument()
    })
  })

  describe('Accessibility', () => {
    beforeEach(() => {
      mockUseAuth.mockReturnValue({
        user: { id: '1', name: 'Admin User', email: '<EMAIL>' },
        isAdmin: () => true,
        isAuthenticated: () => true,
        login: vi.fn(),
        logout: vi.fn(),
        isLoading: false,
      })
    })

    it('should have proper heading structure', () => {
      renderWithProviders(<AdminDashboardOverview />)

      const mainHeading = screen.getByRole('heading', { level: 1 })
      expect(mainHeading).toHaveTextContent('Admin Dashboard')
    })

    it('should have accessible error messages', () => {
      const error = new Error('Test error')
      renderWithProviders(<AdminDashboardOverview error={error} />)

      const errorHeading = screen.getByRole('heading', { level: 3 })
      expect(errorHeading).toHaveTextContent('Dashboard Error')
    })
  })
})
