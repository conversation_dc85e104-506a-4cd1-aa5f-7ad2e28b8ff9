/**
 * Component Management Integration Tests
 * Tests for complete component management workflows
 */

import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { render, screen, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { rest } from 'msw'
import { setupServer } from 'msw/node'
import React from 'react'
import { ComponentFilters } from '../../components/molecules/ComponentFilters'
import { ComponentSearchBar } from '../../components/molecules/ComponentSearchBar'
import { BulkOperationsPanel } from '../../components/organisms/BulkOperationsPanel'
import { ComponentList } from '../../components/organisms/ComponentList'
import type { ComponentRead } from '../../schemas'

// Mock data
const mockComponents: ComponentRead[] = [
  {
    id: 1,
    name: 'Siemens Contactor',
    manufacturer: 'Siemens',
    model_number: 'SC-001',
    component_type: 'contactor',
    category: 'SWITCH',
    unit_price: 45.99,
    currency: 'EUR',
    is_active: true,
    is_preferred: false,
    stock_status: 'available',
    created_at: '2024-01-01T00:00:00Z',
    updated_at: '2024-01-01T00:00:00Z',
  },
  {
    id: 2,
    name: 'ABB Circuit Breaker',
    manufacturer: 'ABB',
    model_number: 'CB-002',
    component_type: 'breaker',
    category: 'SWITCH',
    unit_price: 89.5,
    currency: 'EUR',
    is_active: true,
    is_preferred: true,
    stock_status: 'limited',
    created_at: '2024-01-02T00:00:00Z',
    updated_at: '2024-01-02T00:00:00Z',
  },
  {
    id: 3,
    name: 'Schneider Relay',
    manufacturer: 'Schneider Electric',
    model_number: 'RL-003',
    component_type: 'relay',
    category: 'SWITCH',
    unit_price: 23.75,
    currency: 'EUR',
    is_active: false,
    is_preferred: false,
    stock_status: 'discontinued',
    created_at: '2024-01-03T00:00:00Z',
    updated_at: '2024-01-03T00:00:00Z',
  },
]

// MSW server setup
const server = setupServer(
  rest.get('/api/v1/components', (req, res, ctx) => {
    const manufacturer = req.url.searchParams.get('manufacturer')
    const search = req.url.searchParams.get('search')
    const isActive = req.url.searchParams.get('is_active')

    let filteredComponents = [...mockComponents]

    if (manufacturer) {
      filteredComponents = filteredComponents.filter((c) =>
        c.manufacturer.toLowerCase().includes(manufacturer.toLowerCase())
      )
    }

    if (search) {
      filteredComponents = filteredComponents.filter(
        (c) =>
          c.name.toLowerCase().includes(search.toLowerCase()) ||
          c.manufacturer.toLowerCase().includes(search.toLowerCase())
      )
    }

    if (isActive === 'true') {
      filteredComponents = filteredComponents.filter((c) => c.is_active)
    }

    return res(
      ctx.json({
        items: filteredComponents,
        total: filteredComponents.length,
        page: 1,
        size: 20,
        pages: 1,
        has_next: false,
        has_prev: false,
      })
    )
  }),

  rest.put('/api/v1/components/bulk', (req, res, ctx) => {
    return res(
      ctx.json({
        success: true,
        updated_count: 2,
        results: [
          { id: 1, success: true },
          { id: 2, success: true },
        ],
      })
    )
  }),

  rest.delete('/api/v1/components/bulk', (req, res, ctx) => {
    return res(
      ctx.json({
        success: true,
        deleted_count: 1,
        results: [{ id: 3, success: true }],
      })
    )
  })
)

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  })

  return <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
}

// Setup and teardown
beforeAll(() => server.listen())
afterEach(() => server.resetHandlers())
afterAll(() => server.close())

describe('Component Management Integration', () => {
  describe('Component List with Search and Filters', () => {
    it('displays components and allows filtering by manufacturer', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <div>
            <ComponentFilters onFiltersChange={() => {}} />
            <ComponentList components={mockComponents} onComponentView={() => {}} />
          </div>
        </TestWrapper>
      )

      // Verify initial components are displayed
      expect(screen.getByText('Siemens Contactor')).toBeInTheDocument()
      expect(screen.getByText('ABB Circuit Breaker')).toBeInTheDocument()
      expect(screen.getByText('Schneider Relay')).toBeInTheDocument()

      // Filter by manufacturer
      const manufacturerSelect = screen.getByRole('combobox', { name: /manufacturer/i })
      await user.click(manufacturerSelect)
      await user.click(screen.getByRole('option', { name: 'Siemens' }))

      // Wait for filter to be applied
      await waitFor(() => {
        expect(screen.getByDisplayValue('Siemens')).toBeInTheDocument()
      })
    })

    it('allows searching for components', async () => {
      const user = userEvent.setup()
      const mockOnSearch = vi.fn()

      render(
        <TestWrapper>
          <ComponentSearchBar onSearch={mockOnSearch} />
        </TestWrapper>
      )

      const searchInput = screen.getByRole('textbox', { name: /search components/i })

      // Type search query
      await user.type(searchInput, 'Siemens')

      // Wait for debounced search
      await waitFor(
        () => {
          expect(mockOnSearch).toHaveBeenCalledWith('Siemens')
        },
        { timeout: 500 }
      )
    })

    it('shows search suggestions and history', async () => {
      const user = userEvent.setup()

      render(
        <TestWrapper>
          <ComponentSearchBar showSuggestions showHistory />
        </TestWrapper>
      )

      const searchInput = screen.getByRole('textbox', { name: /search components/i })

      // Focus input to show suggestions
      await user.click(searchInput)

      // Check for suggestions
      await waitFor(() => {
        expect(screen.getByText(/suggestions/i)).toBeInTheDocument()
      })
    })
  })

  describe('Bulk Operations', () => {
    it('allows selecting multiple components and performing bulk operations', async () => {
      const user = userEvent.setup()
      const mockBulkEdit = vi.fn().mockResolvedValue(undefined)

      render(
        <TestWrapper>
          <div>
            <BulkOperationsPanel components={mockComponents} onBulkEdit={mockBulkEdit} />
            <ComponentList components={mockComponents} showBulkActions onComponentView={() => {}} />
          </div>
        </TestWrapper>
      )

      // Select first two components
      const checkboxes = screen.getAllByRole('checkbox', { name: /select component/i })
      await user.click(checkboxes[0])
      await user.click(checkboxes[1])

      // Verify bulk operations panel appears
      await waitFor(() => {
        expect(screen.getByText(/2 selected/i)).toBeInTheDocument()
      })

      // Click bulk edit
      const editButton = screen.getByRole('button', { name: /edit/i })
      await user.click(editButton)

      // Verify bulk edit was called
      expect(mockBulkEdit).toHaveBeenCalled()
    })

    it('shows confirmation dialog for destructive operations', async () => {
      const user = userEvent.setup()
      const mockBulkDelete = vi.fn().mockResolvedValue(undefined)

      render(
        <TestWrapper>
          <div>
            <BulkOperationsPanel components={mockComponents} onBulkDelete={mockBulkDelete} />
            <ComponentList
              components={mockComponents.slice(0, 1)}
              showBulkActions
              onComponentView={() => {}}
            />
          </div>
        </TestWrapper>
      )

      // Select component
      const checkbox = screen.getByRole('checkbox', { name: /select component/i })
      await user.click(checkbox)

      // Click delete
      const deleteButton = screen.getByRole('button', { name: /delete/i })
      await user.click(deleteButton)

      // Verify confirmation dialog appears
      await waitFor(() => {
        expect(screen.getByText(/delete components/i)).toBeInTheDocument()
        expect(screen.getByText(/cannot be undone/i)).toBeInTheDocument()
      })

      // Confirm deletion
      const confirmButton = screen.getByRole('button', { name: /confirm/i })
      await user.click(confirmButton)

      // Verify delete was called
      await waitFor(() => {
        expect(mockBulkDelete).toHaveBeenCalled()
      })
    })
  })

  describe('Component Card Interactions', () => {
    it('allows toggling preferred status', async () => {
      const user = userEvent.setup()
      const mockTogglePreferred = vi.fn()

      render(
        <TestWrapper>
          <ComponentList
            components={mockComponents.slice(0, 1)}
            onComponentTogglePreferred={mockTogglePreferred}
            onComponentView={() => {}}
          />
        </TestWrapper>
      )

      // Find and click preferred button
      const preferredButton = screen.getByRole('button', { name: /add to preferred/i })
      await user.click(preferredButton)

      expect(mockTogglePreferred).toHaveBeenCalledWith(mockComponents[0])
    })

    it('opens component details on card click', async () => {
      const user = userEvent.setup()
      const mockOnView = vi.fn()

      render(
        <TestWrapper>
          <ComponentList components={mockComponents.slice(0, 1)} onComponentView={mockOnView} />
        </TestWrapper>
      )

      // Click on component card
      const componentCard = screen.getByRole('article')
      await user.click(componentCard)

      expect(mockOnView).toHaveBeenCalledWith(mockComponents[0])
    })

    it('supports keyboard navigation', async () => {
      const user = userEvent.setup()
      const mockOnView = vi.fn()

      render(
        <TestWrapper>
          <ComponentList components={mockComponents.slice(0, 1)} onComponentView={mockOnView} />
        </TestWrapper>
      )

      // Tab to component card and press Enter
      const componentCard = screen.getByRole('article')
      componentCard.focus()
      await user.keyboard('{Enter}')

      expect(mockOnView).toHaveBeenCalledWith(mockComponents[0])
    })
  })

  describe('View Mode Switching', () => {
    it('allows switching between grid, list, and table views', async () => {
      const user = userEvent.setup()
      const mockViewModeChange = vi.fn()

      render(
        <TestWrapper>
          <ComponentList
            components={mockComponents}
            onViewModeChange={mockViewModeChange}
            onComponentView={() => {}}
          />
        </TestWrapper>
      )

      // Switch to list view
      const listViewButton = screen.getByRole('button', { name: /list view/i })
      await user.click(listViewButton)

      expect(mockViewModeChange).toHaveBeenCalledWith('list')

      // Switch to table view
      const tableViewButton = screen.getByRole('button', { name: /table view/i })
      await user.click(tableViewButton)

      expect(mockViewModeChange).toHaveBeenCalledWith('table')
    })
  })

  describe('Sorting and Pagination', () => {
    it('allows sorting by different fields', async () => {
      const user = userEvent.setup()
      const mockSortChange = vi.fn()

      render(
        <TestWrapper>
          <ComponentList
            components={mockComponents}
            onSortChange={mockSortChange}
            onComponentView={() => {}}
          />
        </TestWrapper>
      )

      // Change sort field
      const sortSelect = screen.getByRole('combobox', { name: /sort by/i })
      await user.click(sortSelect)
      await user.click(screen.getByRole('option', { name: /manufacturer/i }))

      expect(mockSortChange).toHaveBeenCalledWith({
        field: 'manufacturer',
        order: 'asc',
      })
    })

    it('toggles sort order when clicking sort button', async () => {
      const user = userEvent.setup()
      const mockSortChange = vi.fn()

      render(
        <TestWrapper>
          <ComponentList
            components={mockComponents}
            sortConfig={{ field: 'name', order: 'asc' }}
            onSortChange={mockSortChange}
            onComponentView={() => {}}
          />
        </TestWrapper>
      )

      // Click sort order button
      const sortOrderButton = screen.getByRole('button', { name: /sort descending/i })
      await user.click(sortOrderButton)

      expect(mockSortChange).toHaveBeenCalledWith({
        field: 'name',
        order: 'desc',
      })
    })
  })

  describe('Error Handling', () => {
    it('displays error message when API fails', async () => {
      server.use(
        rest.get('/api/v1/components', (req, res, ctx) => {
          return res(ctx.status(500), ctx.json({ detail: 'Server error' }))
        })
      )

      render(
        <TestWrapper>
          <ComponentList components={[]} error="Server error" onComponentView={() => {}} />
        </TestWrapper>
      )

      expect(screen.getByText(/error loading components/i)).toBeInTheDocument()
      expect(screen.getByText(/server error/i)).toBeInTheDocument()
    })

    it('displays empty state when no components found', () => {
      render(
        <TestWrapper>
          <ComponentList components={[]} onComponentView={() => {}} />
        </TestWrapper>
      )

      expect(screen.getByText(/no components found/i)).toBeInTheDocument()
    })
  })

  describe('Loading States', () => {
    it('displays loading skeleton while fetching data', () => {
      render(
        <TestWrapper>
          <ComponentList components={[]} loading onComponentView={() => {}} />
        </TestWrapper>
      )

      expect(screen.getByTestId('component-list-loading')).toBeInTheDocument()
      expect(screen.getAllByRole('generic')).toHaveLength(6) // 6 skeleton items
    })
  })
})
